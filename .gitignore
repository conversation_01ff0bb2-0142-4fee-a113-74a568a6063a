# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# AI Models and large files
models/*.pth
models/*.bin
models/*.safetensors
*.h5
*.pkl
*.joblib

# Uploaded files
uploads/
data/answer_images/*.jpg
data/answer_images/*.jpeg
data/answer_images/*.png
data/answer_images/*.pdf

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# PyTorch
*.pt
*.pth

# TensorFlow
*.pb
*.tflite

# Database
*.db
*.sqlite
*.sqlite3

# Docker
.dockerignore

# Backup files
*.bak
*.backup

# API keys and secrets
secrets/
*.key
*.pem
*.crt

# Build outputs
build/
dist/
out/

# Test coverage
coverage/
.coverage
htmlcov/

# Pytest
.pytest_cache/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json