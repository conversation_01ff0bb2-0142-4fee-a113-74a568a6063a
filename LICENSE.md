# MIT License

Copyright (c) 2024 EduSarathi

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

## Third-Party Licenses

This project uses several third-party libraries and services:

### Frontend Dependencies
- React.js - MIT License
- Tailwind CSS - MIT License
- Axios - MIT License

### Backend Dependencies
- Node.js - MIT License
- Express.js - MIT License
- MongoDB - Server Side Public License (SSPL)
- Mongoose - MIT License

### AI/ML Dependencies
- Python - Python Software Foundation License
- FastAPI - MIT License
- Transformers (Hugging Face) - Apache License 2.0
- PyTorch - BSD License
- OpenAI API - Commercial License (API usage)

### Additional Services
- Bhashini API - Government of India (Terms of Use apply)
- Google Translate API - Google Cloud Terms of Service

## Attribution

This project includes educational content and methodologies that may be derived from various educational standards and curricula. Proper attribution is given where applicable.

## Disclaimer

This software is provided for educational purposes. Users are responsible for ensuring compliance with applicable laws and regulations in their jurisdiction when using this software for educational activities.

The AI-generated content should be reviewed by qualified educators before use in actual teaching scenarios.