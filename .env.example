# Database Configuration
MONGODB_URI=mongodb://localhost:27017/edusarathi
DB_NAME=edusarathi

# Server Configuration
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# AI API Keys
OPENAI_API_KEY=your_openai_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
GEMINI_API_KEY=AIzaSyB1_kmBdinFeeFAKAgjUpDsjYko_pSOOGs
GOOGLE_AI_API_KEY=AIzaSyB1_kmBdinFeeFAKAgjUpDsjYko_pSOOGs

# Bhashini API (for translation)
BHASHINI_API_KEY=your_bhashini_api_key_here
BHASHINI_USER_ID=your_bhashini_user_id_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=7d

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Python AI Service (Legacy)
AI_SERVICE_URL=http://localhost:8000
AI_SERVICE_TIMEOUT=30000

# Gemini AI Service (Primary)
GEMINI_SERVICE_URL=http://localhost:8001
GEMINI_SERVICE_TIMEOUT=60000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log