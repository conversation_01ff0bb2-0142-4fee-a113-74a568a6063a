{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "AI-powered educational platform for curriculum generation, quiz creation, and assessment", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "frontend": "cd frontend && npm start", "backend": "nodemon backend/server.js", "install-all": "npm install && cd frontend && npm install", "dev-all": "concurrently \"npm run backend\" \"npm run frontend\""}, "keywords": ["education", "ai", "curriculum", "quiz", "assessment"], "author": "EduSarathi Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "winston": "^3.10.0", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "joi": "^17.9.2", "sharp": "^0.32.5"}, "devDependencies": {"nodemon": "^3.0.1", "concurrently": "^8.2.0"}}