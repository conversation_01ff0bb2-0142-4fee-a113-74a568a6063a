{"name": "ed<PERSON><PERSON><PERSON>-backend", "version": "1.0.0", "description": "Backend API for EduSarathi", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "dotenv": "^16.3.1", "winston": "^3.10.0", "multer": "^1.4.5-lts.1", "axios": "^1.5.0"}, "devDependencies": {"nodemon": "^3.0.1"}}