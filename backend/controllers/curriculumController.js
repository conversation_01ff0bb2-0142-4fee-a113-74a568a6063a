const Curriculum = require('../models/Curriculum');
const logger = require('../utils/logger');
const axios = require('axios');

// Generate curriculum using AI
const generateCurriculum = async (req, res) => {
  try {
    const {
      subject,
      grade,
      duration,
      topics,
      learningObjectives,
      difficulty = 'intermediate',
      language = 'en'
    } = req.body;

    // Validate required fields
    if (!subject || !grade || !duration) {
      return res.status(400).json({
        success: false,
        message: 'Subject, grade, and duration are required'
      });
    }

    logger.info(`Generating curriculum for ${subject} - Grade ${grade}`);

    // Call Gemini AI service to generate curriculum (primary)
    let aiResponse;
    try {
      const geminiServiceUrl = process.env.GEMINI_SERVICE_URL || 'http://localhost:8001';
      aiResponse = await axios.post(`${geminiServiceUrl}/curriculum/generate`, {
        subject,
        grade: parseInt(grade),
        duration,
        focus_areas: topics || [],
        learningObjectives: learningObjectives || [],
        difficulty,
        language
      }, {
        timeout: parseInt(process.env.GEMINI_SERVICE_TIMEOUT) || 60000
      });
    } catch (geminiError) {
      logger.warn('Gemini service unavailable, falling back to legacy AI service:', geminiError.message);
      
      // Fallback to legacy AI service
      aiResponse = await axios.post(`${process.env.AI_SERVICE_URL}/curriculum/generate`, {
        subject,
        grade,
        duration,
        topics: topics || [],
        learningObjectives: learningObjectives || [],
        difficulty,
        language
      }, {
        timeout: parseInt(process.env.AI_SERVICE_TIMEOUT) || 30000
      });
    }

    const generatedCurriculum = aiResponse.data.success ? aiResponse.data.data : aiResponse.data;

    // Create curriculum in database
    const curriculum = new Curriculum({
      title: generatedCurriculum.title || `${subject} Curriculum - Grade ${grade}`,
      subject,
      grade,
      duration,
      description: generatedCurriculum.description,
      learningObjectives: generatedCurriculum.learningObjectives || learningObjectives || [],
      topics: generatedCurriculum.topics || [],
      prerequisites: generatedCurriculum.prerequisites || [],
      resources: generatedCurriculum.resources || [],
      assessmentStrategy: generatedCurriculum.assessmentStrategy || {},
      difficulty,
      language,
      tags: generatedCurriculum.tags || [subject.toLowerCase(), grade.toLowerCase()],
      createdBy: req.user?.id || '000000000000000000000000', // Default user ID for demo
      metadata: {
        aiGenerated: true,
        model: generatedCurriculum.model || 'gpt-3.5-turbo',
        generationTime: generatedCurriculum.generationTime || 0
      }
    });

    const savedCurriculum = await curriculum.save();

    logger.info(`Curriculum generated successfully: ${savedCurriculum._id}`);

    res.status(201).json({
      success: true,
      message: 'Curriculum generated successfully',
      data: savedCurriculum
    });

  } catch (error) {
    logger.error('Error generating curriculum:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      return res.status(503).json({
        success: false,
        message: 'AI service is currently unavailable. Please try again later.'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to generate curriculum',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all curricula
const getCurricula = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      subject,
      grade,
      difficulty,
      status = 'published',
      search
    } = req.query;

    // Build filter object
    const filter = { status };
    
    if (subject) filter.subject = new RegExp(subject, 'i');
    if (grade) filter.grade = grade;
    if (difficulty) filter.difficulty = difficulty;
    if (search) {
      filter.$or = [
        { title: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') },
        { tags: new RegExp(search, 'i') }
      ];
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sort: { createdAt: -1 },
      populate: {
        path: 'createdBy',
        select: 'name email'
      }
    };

    const curricula = await Curriculum.paginate(filter, options);

    res.status(200).json({
      success: true,
      data: curricula
    });

  } catch (error) {
    logger.error('Error fetching curricula:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch curricula'
    });
  }
};

// Get curriculum by ID
const getCurriculumById = async (req, res) => {
  try {
    const { id } = req.params;

    const curriculum = await Curriculum.findById(id)
      .populate('createdBy', 'name email')
      .populate('usage.ratings.user', 'name');

    if (!curriculum) {
      return res.status(404).json({
        success: false,
        message: 'Curriculum not found'
      });
    }

    // Increment views
    await curriculum.incrementViews();

    res.status(200).json({
      success: true,
      data: curriculum
    });

  } catch (error) {
    logger.error('Error fetching curriculum:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch curriculum'
    });
  }
};

// Update curriculum
const updateCurriculum = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated directly
    delete updates.createdBy;
    delete updates.usage;
    delete updates.metadata;

    const curriculum = await Curriculum.findByIdAndUpdate(
      id,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).populate('createdBy', 'name email');

    if (!curriculum) {
      return res.status(404).json({
        success: false,
        message: 'Curriculum not found'
      });
    }

    logger.info(`Curriculum updated: ${curriculum._id}`);

    res.status(200).json({
      success: true,
      message: 'Curriculum updated successfully',
      data: curriculum
    });

  } catch (error) {
    logger.error('Error updating curriculum:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to update curriculum'
    });
  }
};

// Delete curriculum
const deleteCurriculum = async (req, res) => {
  try {
    const { id } = req.params;

    const curriculum = await Curriculum.findByIdAndDelete(id);

    if (!curriculum) {
      return res.status(404).json({
        success: false,
        message: 'Curriculum not found'
      });
    }

    logger.info(`Curriculum deleted: ${id}`);

    res.status(200).json({
      success: true,
      message: 'Curriculum deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting curriculum:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to delete curriculum'
    });
  }
};

// Add rating to curriculum
const addRating = async (req, res) => {
  try {
    const { id } = req.params;
    const { rating, comment } = req.body;
    const userId = req.user?.id || '000000000000000000000000';

    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: 'Rating must be between 1 and 5'
      });
    }

    const curriculum = await Curriculum.findById(id);

    if (!curriculum) {
      return res.status(404).json({
        success: false,
        message: 'Curriculum not found'
      });
    }

    await curriculum.addRating(userId, rating, comment);

    res.status(200).json({
      success: true,
      message: 'Rating added successfully',
      data: {
        averageRating: curriculum.averageRating,
        totalRatings: curriculum.usage.ratings.length
      }
    });

  } catch (error) {
    logger.error('Error adding rating:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to add rating'
    });
  }
};

// Get popular curricula
const getPopularCurricula = async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const curricula = await Curriculum.find({ 
      status: 'published' 
    })
    .sort({ 
      'usage.views': -1, 
      'usage.ratings': -1 
    })
    .limit(parseInt(limit))
    .populate('createdBy', 'name')
    .select('title subject grade difficulty averageRating usage.views createdAt');

    res.status(200).json({
      success: true,
      data: curricula
    });

  } catch (error) {
    logger.error('Error fetching popular curricula:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch popular curricula'
    });
  }
};

// Get curriculum statistics
const getCurriculumStats = async (req, res) => {
  try {
    const stats = await Curriculum.aggregate([
      {
        $group: {
          _id: null,
          totalCurricula: { $sum: 1 },
          publishedCurricula: {
            $sum: { $cond: [{ $eq: ['$status', 'published'] }, 1, 0] }
          },
          totalViews: { $sum: '$usage.views' },
          averageRating: { $avg: { $avg: '$usage.ratings.rating' } }
        }
      }
    ]);

    const subjectStats = await Curriculum.aggregate([
      { $match: { status: 'published' } },
      {
        $group: {
          _id: '$subject',
          count: { $sum: 1 },
          averageRating: { $avg: { $avg: '$usage.ratings.rating' } }
        }
      },
      { $sort: { count: -1 } }
    ]);

    const gradeStats = await Curriculum.aggregate([
      { $match: { status: 'published' } },
      {
        $group: {
          _id: '$grade',
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: {
        overview: stats[0] || {
          totalCurricula: 0,
          publishedCurricula: 0,
          totalViews: 0,
          averageRating: 0
        },
        bySubject: subjectStats,
        byGrade: gradeStats
      }
    });

  } catch (error) {
    logger.error('Error fetching curriculum stats:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch curriculum statistics'
    });
  }
};

module.exports = {
  generateCurriculum,
  getCurricula,
  getCurriculumById,
  updateCurriculum,
  deleteCurriculum,
  addRating,
  getPopularCurricula,
  getCurriculumStats
};